package com.jz.barriersystem.answer;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 【重要提醒】<br>
 * 在当前类或当前answer包中，实现相关逻辑<br>
 *
 * 闯关用户调度服务
 */
public class ThroughBarrierSystemService {

	/**
	 * 用户原始签到信息
	 */
	private List<String> signInLines;

	/**
	 * Parsed user list, sorted by sign-in time and user ID
	 */
	private List<User> users;

	/**
	 * Gate information
	 */
	private static final int GT_02_DURATION = 2; // 2 minutes
	private static final int GT_03_DURATION = 3; // 3 minutes
	private static final int GT_04_DURATION = 4; // 4 minutes

	/**
	 * User information class
	 */
	private static class User implements Comparable<User> {
		String userId;
		LocalTime signInTime;

		public User(String userId, LocalTime signInTime) {
			this.userId = userId;
			this.signInTime = signInTime;
		}

		@Override
		public int compareTo(User other) {
			// Sort by sign-in time first, then by user ID
			int timeCompare = this.signInTime.compareTo(other.signInTime);
			if (timeCompare != 0) {
				return timeCompare;
			}
			return this.userId.compareTo(other.userId);
		}

		@Override
		public String toString() {
			return userId + " (" + signInTime + ")";
		}
	}
	
	public static ThroughBarrierSystemService build(List<String> signInLines) {
		// Initialize service instance
		ThroughBarrierSystemService throughBarrierSystemService = new ThroughBarrierSystemService(signInLines);
		// Optional data initialization
		throughBarrierSystemService.postConstruct();
		// Return service instance
		return throughBarrierSystemService;
	}

	/**
	 * Service instance constructor
	 *
	 * @param signInLines Original user sign-in information
	 */
	private ThroughBarrierSystemService(List<String> signInLines) {
		this.signInLines = signInLines;
	}

	/**
	 * Optional custom data initialization
	 */
	private void postConstruct() {
		// Parse user sign-in information
		users = new ArrayList<>();
		DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");

		for (String line : signInLines) {
			String[] parts = line.split("\t");
			if (parts.length == 2) {
				String userId = parts[0];
				LocalTime signInTime = LocalTime.parse(parts[1], timeFormatter);
				users.add(new User(userId, signInTime));
			}
		}

		// Sort by sign-in time and user ID
		Collections.sort(users);
	}
	

	/**
	 * Question 1: Answer position
	 * @return Return completion time, format: yyyy-MM-dd HH:mm:ss
	 */
	public String answer1() {
		// GT_02 -> GT_03 -> GT_04 sequence, find earliest completion time
		int[] gateDurations = {GT_02_DURATION, GT_03_DURATION, GT_04_DURATION};
		List<LocalTime> completionTimes = simulateSequentialGates(gateDurations);

		// Find the earliest completion time
		LocalTime earliestCompletion = completionTimes.stream().min(LocalTime::compareTo).orElse(null);

		if (earliestCompletion != null) {
			// Assume today's date, format as yyyy-MM-dd HH:mm:ss
			LocalDateTime dateTime = LocalDateTime.of(2024, 1, 1, earliestCompletion.getHour(),
					earliestCompletion.getMinute(), earliestCompletion.getSecond());
			return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
		}

		return null;
	}

	/**
	 * Question 2: Answer position
	 * @return Return completion time, format: yyyy-MM-dd HH:mm:ss
	 */
	public String answer2() {
		// GT_04 -> GT_03 -> GT_02 sequence, find latest completion time
		int[] gateDurations = {GT_04_DURATION, GT_03_DURATION, GT_02_DURATION};
		List<LocalTime> completionTimes = simulateSequentialGates(gateDurations);

		// Find the latest completion time
		LocalTime latestCompletion = completionTimes.stream().max(LocalTime::compareTo).orElse(null);

		if (latestCompletion != null) {
			// Assume today's date, format as yyyy-MM-dd HH:mm:ss
			LocalDateTime dateTime = LocalDateTime.of(2024, 1, 1, latestCompletion.getHour(),
					latestCompletion.getMinute(), latestCompletion.getSecond());
			return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
		}

		return null;
	}

	/**
	 * Question 3: Answer position
	 * @return Return completion time, format: yyyy-MM-dd HH:mm:ss
	 */
	public String answer3() {
		// Each user only needs to pass 1 gate, how to schedule for shortest total time
		LocalTime finalTime = simulateOptimalSingleGate();

		if (finalTime != null) {
			// Assume today's date, format as yyyy-MM-dd HH:mm:ss
			LocalDateTime dateTime = LocalDateTime.of(2024, 1, 1, finalTime.getHour(),
					finalTime.getMinute(), finalTime.getSecond());
			return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
		}

		return null;
	}

	/**
	 * Simulate sequential gates (each user must pass all gates in sequence)
	 * @param gateDurations Gate duration array
	 * @return List of completion times for each user
	 */
	private List<LocalTime> simulateSequentialGates(int[] gateDurations) {
		List<LocalTime> completionTimes = new ArrayList<>();

		// Next available time for each gate
		LocalTime[] gateAvailableTime = new LocalTime[gateDurations.length];
		Arrays.fill(gateAvailableTime, LocalTime.of(8, 0, 0)); // Assume start at 8:00

		for (User user : users) {
			LocalTime currentTime = user.signInTime;

			// User passes each gate in sequence
			for (int i = 0; i < gateDurations.length; i++) {
				// User arrival time at gate is max of current time and gate available time
				LocalTime startTime = currentTime.isAfter(gateAvailableTime[i]) ?
						currentTime : gateAvailableTime[i];

				// Gate completion time
				LocalTime endTime = startTime.plusMinutes(gateDurations[i]);

				// Update next available time for this gate
				gateAvailableTime[i] = endTime;

				// Update user's current time to completion time of current gate
				currentTime = endTime;
			}

			// Record user's completion time for all gates
			completionTimes.add(currentTime);
		}

		return completionTimes;
	}

	/**
	 * Simulate optimal single gate scheduling (each user only needs to pass 1 gate)
	 * @return Final completion time
	 */
	private LocalTime simulateOptimalSingleGate() {
		// Greedy algorithm: prioritize assignment to shortest duration gate
		// Next available time for each gate
		LocalTime gt02Available = LocalTime.of(8, 0, 0);
		LocalTime gt03Available = LocalTime.of(8, 0, 0);
		LocalTime gt04Available = LocalTime.of(8, 0, 0);

		LocalTime lastCompletionTime = LocalTime.of(8, 0, 0);

		for (User user : users) {
			// Calculate user completion time for each gate
			LocalTime gt02Start = user.signInTime.isAfter(gt02Available) ? user.signInTime : gt02Available;
			LocalTime gt02End = gt02Start.plusMinutes(GT_02_DURATION);

			LocalTime gt03Start = user.signInTime.isAfter(gt03Available) ? user.signInTime : gt03Available;
			LocalTime gt03End = gt03Start.plusMinutes(GT_03_DURATION);

			LocalTime gt04Start = user.signInTime.isAfter(gt04Available) ? user.signInTime : gt04Available;
			LocalTime gt04End = gt04Start.plusMinutes(GT_04_DURATION);

			// Choose the gate with earliest completion time
			if (gt02End.isBefore(gt03End) && gt02End.isBefore(gt04End)) {
				// Choose GT_02
				gt02Available = gt02End;
				lastCompletionTime = gt02End.isAfter(lastCompletionTime) ? gt02End : lastCompletionTime;
			} else if (gt03End.isBefore(gt04End)) {
				// Choose GT_03
				gt03Available = gt03End;
				lastCompletionTime = gt03End.isAfter(lastCompletionTime) ? gt03End : lastCompletionTime;
			} else {
				// Choose GT_04
				gt04Available = gt04End;
				lastCompletionTime = gt04End.isAfter(lastCompletionTime) ? gt04End : lastCompletionTime;
			}
		}

		return lastCompletionTime;
	}

}
