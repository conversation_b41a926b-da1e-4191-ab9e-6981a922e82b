package com.jz.barriersystem.answer;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.PriorityQueue;

/**
 * 【重要提醒】<br>
 * 在当前类或当前answer包中，实现相关逻辑<br>
 * 
 * 闯关用户调度服务
 */
public class ThroughBarrierSystemService {

	/**
	 * 用户原始签到信息
	 */
	private List<String> slines;

    // 输出时需要加上的日期前缀
    private static final String DATE_PREFIX = "2024-12-28 ";
    // 存储所有用户
    private List<Unameid> uno;
    // 存储所有关卡
    private List<Gidno> gidno;
    // 时间格式化工具
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    
	public static ThroughBarrierSystemService build(List<String> signInLines) {
		// 初始化服务实例
		ThroughBarrierSystemService throughBarrierSystemService = new ThroughBarrierSystemService(signInLines);
		// [可选]数据统一初始化处理
		throughBarrierSystemService.postConstruct();
		// 返回服务实例
		return throughBarrierSystemService;
	}

	/**
	 * 服务实例构造器
	 * 
	 * @param signInLines 用户原始签到信息
	 */
	private ThroughBarrierSystemService(List<String> signInLines) {
		this.slines = signInLines;
	}

	/**
	 * 【可选】自定义数据初始化
	 */
	private void postConstruct() {
        uno = new ArrayList<>();
        for (String li : slines) {
            // 分割每行数据
            String[] parts = li.split("\t");
            String tstr = parts[1];
            String uid = parts[0];
            // 创建用户对象并添加到列表
            uno.add(new Unameid(uid, LocalTime.parse(tstr, TIME_FORMATTER)));
        }
        // 按签到时间和用户ID排序
        Collections.sort(uno);

        // 第2步：创建关卡信息
        gidno = new ArrayList<>();
        gidno.add(new Gidno("GT_04", 4)); 
        gidno.add(new Gidno("GT_03", 3));   
        gidno.add(new Gidno("GT_02", 2));  
	}
	

	/**
	 * 第1问：答题位置
	 * @return 返回最后完成时间，时间格式为：yyyy-MM-dd HH:mm:ss
	 */
	public String answer1() {
		 List<Gidno> gateOrder = Arrays.asList(gidno.get(0), gidno.get(1),  gidno.get(2)  );

		        LocalTime sftimie = LocalTime.MAX;
		        
		        for (Unameid user : uno) {
		            LocalTime uftime = Common.processUserThroughGates(user.getStime(), gateOrder);
		            if (uftime.isBefore(sftimie)) {
		            	sftimie = uftime;
		            }
		        }
		        return DATE_PREFIX + sftimie.format(TIME_FORMATTER);
	}

	/**
	 * 第2问：答题位置
	 * @return 返回最后完成时间，时间格式为：yyyy-MM-dd HH:mm:ss
	 */
	public String answer2() {
		 List<Gidno> gateOrder = Arrays.asList( gidno.get(2),  gidno.get(1),   gidno.get(0)  );
		        LocalTime lftime = LocalTime.MIN;
		        
		        // 让每个用户按顺序通过所有关卡
		        for (Unameid user : uno) {
		            // 获取用户完成时间
		            LocalTime ufile = Common.processUserThroughGates(user.getStime(), gateOrder);
		            // 更新最晚完成时间
		            if (ufile.isAfter(lftime)) {
		            	lftime = ufile;
		            }
		        }

		        // 返回格式化后的时间
		        return DATE_PREFIX + lftime.format(TIME_FORMATTER);
	}

	/**
	 * 第3问：答题位置
	 * @return 返回最后完成时间，时间格式为：yyyy-MM-dd HH:mm:ss
	 */
	public String answer3() {

        PriorityQueue<Gidno> agates = new PriorityQueue<>((g1, g2) -> g1.getNtime().compareTo(g2.getNtime()));
        agates.addAll(gidno);
        LocalTime ltime = LocalTime.MIN;
        for (Unameid user : uno) {
            Gidno gate = agates.poll();
            LocalTime stime = user.getStime();
            if (stime.isBefore(gate.getNtime())) {
            	stime = gate.getNtime();
            }
            LocalTime ftime = gate.getFinishTime(stime);
            gate.setNtime(ftime);
            agates.offer(gate);
            if (ftime.isAfter(ltime)) {
            	ltime = ftime;
            }
        }

        return DATE_PREFIX + ltime.format(TIME_FORMATTER);
	}
	

    


	
}
