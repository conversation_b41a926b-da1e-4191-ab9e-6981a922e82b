package com.jz.barriersystem.answer;

import java.util.List;

/**
 * 【重要提醒】<br>
 * 在当前类或当前answer包中，实现相关逻辑<br>
 * 
 * 闯关用户调度服务
 */
public class ThroughBarrierSystemService {

	/**
	 * 用户原始签到信息
	 */
	private List<String> signInLines;
	
	public static ThroughBarrierSystemService build(List<String> signInLines) {
		// 初始化服务实例
		ThroughBarrierSystemService throughBarrierSystemService = new ThroughBarrierSystemService(signInLines);
		// [可选]数据统一初始化处理
		throughBarrierSystemService.postConstruct();
		// 返回服务实例
		return throughBarrierSystemService;
	}

	/**
	 * 服务实例构造器
	 * 
	 * @param signInLines 用户原始签到信息
	 */
	private ThroughBarrierSystemService(List<String> signInLines) {
		this.signInLines = signInLines;
	}

	/**
	 * 【可选】自定义数据初始化
	 */
	private void postConstruct() {
		// TODO [可选]可以在这边解析votePaperLines属性的内容。并针对答题的数据处理需求，构造一些自定义的属性或对象
	}
	

	/**
	 * 第1问：答题位置
	 * @return 返回最后完成时间，时间格式为：yyyy-MM-dd HH:mm:ss
	 */
	public String answer1() {
		
		// TODO 作答第1问位置
		
		return null;
	}

	/**
	 * 第2问：答题位置
	 * @return 返回最后完成时间，时间格式为：yyyy-MM-dd HH:mm:ss
	 */
	public String answer2() {

		// TODO 作答第2问位置

		return null;
	}

	/**
	 * 第3问：答题位置
	 * @return 返回最后完成时间，时间格式为：yyyy-MM-dd HH:mm:ss
	 */
	public String answer3() {

		// TODO 作答第3问位置

		return null;
	}
	
}
