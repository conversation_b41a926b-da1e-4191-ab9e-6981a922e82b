package com.jz.barriersystem.answer;

import java.time.LocalTime;

public class <PERSON><PERSON><PERSON> implements Comparable<Unameid> {
    private LocalTime stime;
    private String no;

    public Unameid(String id, LocalTime signInTime) {
        this.no = id;
        this.stime = signInTime;
    }

    public LocalTime getStime() {
        return stime;
    }
    
    public String getNo() {
        return no;
    }


    @Override
    public int compareTo(Unameid other) {
        int timeCompare = this.stime.compareTo(other.stime);
        
        if (timeCompare == 0) {
            return this.no.compareTo(other.no);
        }
        return timeCompare;
    }
}