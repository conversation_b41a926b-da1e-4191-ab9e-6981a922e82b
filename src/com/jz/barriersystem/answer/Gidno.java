package com.jz.barriersystem.answer;

import java.time.LocalTime;

public class Gidno {
    private boolean opied;
    private LocalTime ntime;
    private String nm;
    private int dur;

    public Gidno(String name, int duration) {
        this.opied = false;
        this.ntime = LocalTime.MIN;
        this.nm = name;
        this.dur = duration;
    }

    public String getNm() {
        return nm;
    }

    public int getDur() {
        return dur;
    }

    public boolean isOpied() {
        return opied;
    }

    public LocalTime getNtime() {
        return ntime;
    }

    public void setNtime(LocalTime time) {
        this.ntime = time;
    }

    public LocalTime getFinishTime(LocalTime startTime) {
        return startTime.plusMinutes(dur);
    }
}
