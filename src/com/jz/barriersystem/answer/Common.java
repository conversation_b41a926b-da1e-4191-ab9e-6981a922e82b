package com.jz.barriersystem.answer;

import java.time.LocalTime;
import java.util.List;

public class Common {
    public static LocalTime processUserThroughGates(LocalTime startTime, List<Gidno> gateOrder) {
        LocalTime currentTime = startTime;
        
        // 按顺序通过每个关卡
        for (Gidno gate : gateOrder) {
            // 如果当前时间早于关卡可用时间，需要等待
            if (currentTime.isBefore(gate.getNtime())) {
                currentTime = gate.getNtime();
            }
            
            // 计算通过当前关卡后的时间
            LocalTime finishTime = gate.getFinishTime(currentTime);
            
            // 更新关卡的下一个可用时间
            gate.setNtime(finishTime);
            
            // 更新当前时间
            currentTime = finishTime;
        }
        
        return currentTime;
    }
}
