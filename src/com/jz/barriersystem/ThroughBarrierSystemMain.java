package com.jz.barriersystem;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;

import com.jz.barriersystem.answer.ThroughBarrierSystemService;

/**
 * 【重要提醒】
 * 1. 不要修改这个类
 * 2. 所有作答内容在answer包中实现
 * 
 * 闯关用户调度系统主入口
 */
public class ThroughBarrierSystemMain {

	public static void main(String[] args) throws IOException {

		// 读取用户签到信息
		List<String> signInLines = Files.readAllLines(new File("user_sign.input").toPath());

		// 获取嘉宾人气投票活动服务实例
		ThroughBarrierSystemService throughBarrierSystemService = ThroughBarrierSystemService.build(signInLines);

		String result1 = throughBarrierSystemService.answer1();
		// 打印结果
		printResult("Result - 1", result1);

		String result2 = throughBarrierSystemService.answer2();
		// 打印结果
		printResult("Result - 2", result2);

		String result3 = throughBarrierSystemService.answer3();
		// 打印结果
		printResult("Result - 3", result3);
	}

	/**
	 * 打印结果
	 * 
	 * @param resultTitle 打印标题
	 * @param result      结果
	 */
	public static void printResult(String resultTitle, String result) {
		// 打印结果标题
		System.out.println("[" + resultTitle + "] ");
		System.out.println("---------------------");
		// 非空校验
		if (null != result) {
			System.out.println(result);
		} else {
			System.out.println("null or empty: " + result);
		}
		// 结尾间隔
		System.out.println();
	}

}
